{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.4", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "a2523ee572d34a1df6925e1e93a0e1f39ad37cdb5f8b2c679e9aafcf756094fd", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.4", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "a2523ee572d34a1df6925e1e93a0e1f3de90b14f1769473594a6179eb0d393b1", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3fbf475d6486dd625b60cdba9f718f0b4", "path": "Color+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c030d6c93a23b0641a13cd17f2269962", "path": "DeviceDetection.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b507607b1e0258d8a3bf7f20e5b7cf7b", "path": "String+Localization.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f34e73c53a9803edca62bf945fc2f9e301", "path": "UIApplication+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f2233ec9639ab2c77671107a091b81ca", "path": "View+Extensions.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3b692731c0070ee9a55ee07d2f0f7a207", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3eea270f1079ed2ddb0eb25847f7cc34e", "path": "爸爸头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f9891c0dac34bf8e3f54eee18a5b16b4", "path": "宝箱未打开.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a590e7907dcdbe06e5dc4b339c9d13e2", "path": "宝箱已打开.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f33b7d0b070956b8033a12526979106065", "path": "产品介绍.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a8df05145a34e37b76f117487c5bfd8d", "path": "初级会员.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3db7ef7a6322de451508a93564b463a17", "path": "道具配置.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f35da5bd4162a576a118fdc3bea12f4587", "path": "登录页面logo.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3bfc1afff18da1f98fe13d5d350f9772a", "path": "高级会员.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e0040a7af856c70aa8ffd5e01304995f", "path": "个人中心插图.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3632717452989beb5f5e8efa5eff85857", "path": "刮刮卡.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f33871d8d9fdf72a11fac14fe45bf250c5", "path": "刮刮卡中奖.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b0afa8679eda79b54742167cf73a2212", "path": "关闭.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f34eb2de9ce96883854fe40014acc62b48", "path": "皇冠.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f380732de594f7c739032ed3514707b7fb", "path": "皇冠(订阅）.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f9ccb5dbcd02c4a3be25138ce3023b7c", "path": "进入.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3238ca488d1fcf443960d426f08b3e8e6", "path": "录音.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f329b5a8353a49e860af73a2690eeb0fc6", "path": "妈妈头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d28c7085562b61c815f1abfa8f078575", "path": "男生头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3242ece7637ef8bb7f3444eb7e949228c", "path": "女生头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f395894a3065c3d96719b9eadc4ba2011b", "path": "其他头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f31a3f5d22f49d828653854bad8d3d5e9c", "path": "全班操作.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b053bc42e15077c0c0b43bcd221e4297", "path": "添加学生.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.jpeg", "guid": "a2523ee572d34a1df6925e1e93a0e1f35d3ac70d9b0ea874c35e3d7c5e95d0b5", "path": "团团转logo.jpg", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a08f712d6203548b344ca174f43b185c", "path": "烟花.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3976c2937042eaabf38633df1a1eef2b8", "path": "banjixinxi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a5c8ca048e0d9cb27c685b7a8553e28f", "path": "chengzhang.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3dfa297a2aff1a531a595fda7033e24e7", "path": "choujiang.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f30483e38453d0eddbf08e259d029c874d", "path": "denglu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f38ab8953c12b57d7676d80a95de86bc7a", "path": "fanhui.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3cf00d1776ebc93539a5c7aacc051ce0e", "path": "fenxi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f392ab1b2a2b557b9220a7b7a406d481d9", "path": "guanyu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3bf6f1d4b74097d684368193181587e95", "path": "guizepeizhi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f31712be0ea125a036fea32d04761ee8c7", "path": "guzhangfankui.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f376e53c883ce702377d9e6546c0bae0da", "path": "huiyuan.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e1caece4c628275bce99145c430f0316", "path": "jianpan.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f36cbbe5427519db23c72b7bab57f342a5", "path": "laoshi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f31d2fab004fe0c7fe536abf1fe3dc1e99", "path": "lingqujilu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f346284edcc675d8cf8605ddedca576d06", "path": "lishi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3fb9dfafed3c8adfcadac32a52207bdae", "path": "logo.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c5c168d3ac663effa6c0147914bccb8f", "path": "shanchu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3fd56530e53dae7eb254ed433a4d3766c", "path": "shouye1_1.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3cea7c77c9d65d5a4af6fe1aa93a5a90e", "path": "sousuo.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f329cd7f11a37739116d96e42a01eb72a8", "path": "tongbu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f36ddc9677fa596403d61745f08511d615", "path": "tuichu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3419149f203fc701ba82028df8c6bcac1", "path": "wode13.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f37c199d40cb99320409b59729e0795968", "path": "yinsizhengce.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d8faa4e0dea332f0454e6cbe0f31460c", "path": "yonghuxieyi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3656b21f09223fc9ef160d2db3ee140c8", "path": "yuyan.png", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3aa1b8ce79476f5be26ce6da079cfbc45", "name": "image", "path": "image", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3ff052a6c2b0c08679a04389b467bbea9", "path": "AIAnalysisModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3fa82df22f3578ef02a8042622e971e02", "path": "BlindBoxItem.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f320aeeb94050bbf0eee5d28dfa48ac1ca", "path": "CoreDataExtensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d2fcc6e5746000d3259b6d84ccd9fb1b", "path": "DataManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d926f781ef1518c6bc7f5d317ae8465f", "path": "DataModelUsageExamples.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d565e8142ba3bb226ba8ba63baa20559", "path": "DateRangeType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f34dfb109b931ded7a9e5f8bd4a1df3ad4", "path": "MemberPointsModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f37fc36b8b37447e7b4f99166d8c55e602", "path": "ScratchCardItem.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3afddcb09be8f57d42621283128796e1c", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3126c56671c6973df83ffd0b1fe55d52a", "path": "AIAnalysisService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a8449f725c659353876c9d998acd04cc", "path": "iCloudSyncManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3021e0153f9b09e2c2ec05cb7367555e3", "path": "SpeechRecognitionService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3e04e428a4c2fecec8e4d80b88bcf9a2b", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d16303db8c2671b0b84afcde31987445", "path": "DesignSystem.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f323594debf1b9fa65bcda204e747a2e02", "path": "iPadLayoutConfig.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d622eddd5b1c74fbc57b84d494562435", "path": "ResponsiveLayoutConfig.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3b11d0af151fe18bb4fd733e12e64605f", "name": "Styles", "path": "Styles", "sourceTree": "<group>", "type": "group"}, {"guid": "a2523ee572d34a1df6925e1e93a0e1f363f6554c20ec3e282cb394a8bce974f7", "name": "Tests", "path": "Tests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a54b284fac24203db8b30db5d84a2cd2", "path": "KeyboardUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f31631ad36f532f634850d159f21b1b4ad", "path": "KeychainManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3ded54059f5235121734f683cd9189b88", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e5d605ec8eee47440534bb0da8c617ef", "path": "AIAnalysisViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f39b7bed2ae6800c622ad58a5ccf4426b5", "path": "GrowthDiaryViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f383942065bc6c46e365a67ec112e2f5ea", "path": "HomeViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f39bd492616e38954ae53007a8f2e39dcf", "path": "MemberDetailViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f33919cf2a0bc02b11f321b23b93d334ca", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3832acb3fc28dfee745d00ddc11333e10", "path": "ChildrenPrivacyPolicyView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f345853aa2a0b000d43d1fedc8c15dd6d0", "path": "PrivacyPolicyView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f357b69bbadccfaaea1f85278ff6df2997", "path": "UserAgreementView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f39c2fc71c83165f5ffd9a37e8bff50da7", "name": "Policy", "path": "Policy", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f360dd4b16e2536b0b2c08902f51520257", "path": "AboutView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f32f547c228a54f82cd052b8e7a5169ef2", "name": "About", "path": "About", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f7377d84279c3957cf4062d0362e8217", "path": "BlindBoxCubeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f61274387279e727a06123c3ab8a2cdc", "path": "BlindBoxGridView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3270dd0833cf9db4d533a46fe00b48993", "path": "ExplosionAnimationView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3979376b4b26ada5d78c885f074afebd8", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f328c4264f8d85eb037344420fb43c75dc", "path": "BlindBoxView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f34fbad65bdf6263b1c7a71839e1c2855f", "path": "BlindBoxViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3cb38012f0d0e474089888345c1acff04", "name": "BlindBox", "path": "BlindBox", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3595c4ce65989a1342f6ae22a01d5ba33", "path": "ActionButtonsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f31620f5f3345ba39c54ef30cfddbf84ed", "path": "AddMemberFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e6030c8c2ab31eed8f26101bcedc4288", "path": "AddRewardFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f31306d6d21c8299dc18d0ab2e5b1fe432", "path": "BlindBoxConfigPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a4f57ee4280e108eced0d7739ef643d4", "path": "CustomRewardExchangeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f315dbd93c490f5439e65e321f79a3cfc8", "path": "CustomTabBar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f34bc9b444fae82843b97067abef8e250d", "path": "DatePickerPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f304c2e810ac8d3b513d133348aec48c18", "path": "DatePickerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c9bf241eb81f1f25b0d800bdcbacaec8", "path": "DateRangePickerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f354b91f4d351f11cf4bfe4f5490300195", "path": "FamilyMemberCardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3916d27849c258d03b4ff977e19b837c1", "path": "FamilyMemberGridView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f39c393ec96f07440a801e33673f642199", "path": "FamilyOperationFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3dba7902e2344040bee5a7ac09a06284e", "path": "FamilyOperationOptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f346b8f989013fd0aaad267e2db4aff9f2", "path": "FamilyTotalScoreView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f37cf81471f2673032aded6df3c59170ff", "path": "KeyboardToolbarView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3eedc87d8ebe3e45096b8f341ff005d3e", "path": "LiquidTabBarBackground.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3600460c5d4cd6a401ac46e9a56372a99", "path": "LiquidTabBarBubble.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f32dc4cc19149cae3664723b04ae38373b", "path": "LiquidTabBarShape.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3dc6bab28b7746aa8122ddadb82289c93", "path": "LotteryConfigPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a0650a232351d69c6c4ef1333a9bb473", "path": "LotteryToolSelectionPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3388fbf84d2f9abc3a4382d367e9baa26", "path": "MemberPickerPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f30be872015293d753bc750406ba7c9d91", "path": "MemberPointsFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3712aab2ecb0119594050545a0b4fcb7e", "path": "MemberPointsOptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3530f0ce374cd5e18b039ac2518beadc1", "path": "MemberRewardExchangeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f371ef1d670dd372106a0a8e8038075c5c", "path": "MemberSelectionPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f32907470d87a6f45f7f1283d949a6314d", "path": "RoleSelectionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f31d0d0dad114dd6255e037cd6816c1e41", "path": "ScratchCardConfigPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f339c2984999d0d1daa3509b73e7b8ebae", "path": "VoiceRecordingButton.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f362299b1b13d2bec9cbd09f0f2d9c921c", "path": "WheelConfigPopupView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f37ea4c46d7b4b88f7110ccee469e6365f", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3bbdfe23fa069f46d30118152db053922", "path": "iCloudSyncStatusView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f32f4328519fc9be7696fc983646282293", "path": "SubscriptionBannerSection.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a68903c43fb106a1ff40b41f23e25d65", "path": "SubscriptionDowngradeHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f354a1b2afb67e7f2f473947031a644bd5", "path": "SystemSettingsSection.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f30beefe5fa7fbc85f0be1e92bfe94c58d", "path": "UserInfoSection.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f379baad7c1db5f33d6cdb0796daea71c8", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"guid": "a2523ee572d34a1df6925e1e93a0e1f3cc8cb71b9e37d2ce8e71ec483e29851e", "name": "Modals", "path": "Modals", "sourceTree": "<group>", "type": "group"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f39442085f9bd4c83704920c9baed11fb7", "name": "Profile", "path": "Profile", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f31643dffbf39a9f9d0fd3edb19787fa90", "path": "ScratchCardCanvasView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f32ad245b9275129e236abe646c70ec567", "path": "ScratchCardGridView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f36541e9b5a0e5decb29c5d5a03bbbc55a", "path": "ScratchCardResultView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3caa56351076722ad6b0fe1760b8fdd45", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3bb69b59499d7046c995bd25afbd2449f", "path": "ScratchCardViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3f3b67943e5683eacb64ed96b618c6b07", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3896e13eafa5ebab123c543210a7ab7f1", "path": "ScratchCardStylePreview.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f714d1991e95aecaadb571fcfe319f9d", "path": "ScratchCardView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f30a01cf27b79b4af5a50a1b78ea984b40", "name": "ScratchCard", "path": "ScratchCard", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3134b8bf7a17f34080b0c7fbc4af9359e", "path": "BackgroundImageLayer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d9475ef8379a110f727b4cfc26778f24", "path": "MembershipAgreementView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d5d8bbb8e9e712c1cf415383d68765e5", "path": "MembershipContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a50abc061c1614434302da918a0a6fa8", "path": "MembershipTabSegment.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3800eda96da37870bc29f792d5c5e3c3c", "path": "SubscriptionUserInfoSection.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f36722909844ee4b0721aa12f9691e0235", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d8781577081383f94e6d658bd4298a77", "path": "SubscriptionView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f34605f18a2458c9a5c7e9b31398b714c9", "name": "Subscription", "path": "Subscription", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f33bd63ec3ee467a6cdff0203d635624d6", "path": "AIAnalysisHistoryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b3400506d64f645e02111ca6b61c7649", "path": "AIAnalysisView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f384d7a9ecbcf60618afb58e562fcd4599", "path": "AIReportDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3953cac2d25be592dc45812de3cbe26dd", "path": "AvatarDisplayTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3664ddda9dc48ae0aab2d164be10ec6d6", "path": "AvatarFixVerificationView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f38d70abd5eea85a46664758a092889108", "path": "BlindBoxBugFixTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d789c662f5069c747f3c0ba7ebdb8cd2", "path": "BlindBoxConfigIntegrationTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d7c141ee275373cb5bf607080aa6ef2c", "path": "BlindBoxIntegrationTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f38c3b72f09d18fdc02d4b522c5633b389", "path": "BlindBoxTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f31a2c52b3a339f3dbef561f4083300a67", "path": "CardComparisonTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3fbd811b49b4eaad0372b987ff685613e", "path": "ExchangeRecordTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f373f1c6f9c03b6270087ab54785abf611", "path": "GrowthDiaryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d4aa06a1fc9c1a94c6a620a0c272f470", "path": "HomeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e83fff3b301c195600d2faaf9ec3cf50", "path": "KeyboardToolbarTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f387617896eafc56e952961e602e831c18", "path": "LotteryConfigTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f313c67a8fd33d947b8fc0c89858226a45", "path": "LotteryOptionsTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3ab369dbb9b605683d2e4ae5ba37aa543", "path": "LotteryOptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f30961a29cb31d9806f05ccfbfa3167f96", "path": "LotteryWheelTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f31ea7b7e7540ca9de785256e57bf2fb12", "path": "LotteryWheelView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f35627a58dec65159b1d77b7cabda75a23", "path": "MainTabView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d067d0d7c791330cf3a101707071f51a", "path": "MemberDetailTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f2eea56eeba060b893b0e911cff31ce6", "path": "MemberDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f361ef4f0fc33797f7f56fe899c3a4a2ac", "path": "ProductIntroductionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d7a0d5945a5ab6d12c81f7ab4fd199f6", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f370da38e6d3fe4a41349702b707784e6d", "path": "ScratchCardTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f332d8e2489a7953f200c11d9dbf458fce", "path": "SubscriptionTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f33b8fa2ebf7b1f3ea63d3ae5d1d2f65a3", "path": "TestPointsFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c539f2669dd824cbaecea06043747b43", "path": "WheelConfigTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f39573d3694144a7e42734975aaf473fdb", "path": "WheelIntegrationTestView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3cafe01391708e303987fafd0a2ac3a9f", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f39e7f7993096e4c1cbd5b03575140bef8", "path": "产品介绍页面实现总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3426643493674ec56afad061d5d7ee303", "path": "成员卡片样式优化总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f32175120dd31fcdfc72b9ebf8331d644f", "path": "弹窗样式统一修改总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f32a1b4c41cc81f3e5518f749876247f29", "path": "订阅页面布局对比分析.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f32e64bdcda44dae572d45bd6e3633e089", "path": "订阅页面使用说明.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f38e5954e1139277640388550307e74155", "path": "兑换记录统一显示修改总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f33a717a3f7cd943862277229f0947b9eb", "path": "刮刮卡编译错误修复报告.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f325bfb7b3015d45387f9986af7578d1cb", "path": "刮刮卡功能实现总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f36f463093e910fc48ab36fe6789b3f6a7", "path": "刮刮卡功能最终验证报告.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f31a04191276e72be2ba83632dc4ac04d8", "path": "刮刮卡界面样式修改总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f31cd518c4945b1dfb26bfb176185a23c2", "path": "刮刮卡配置持久化修复报告.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3051b1a49f218b146890dc0cb1000a4d9", "path": "刮刮卡使用指南.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e167be3214d79bde48990c0153e31939", "path": "刮刮卡随机性修复报告.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3582a36d7d8b03d5156f628c7782aade5", "path": "关于页面实现总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3999992dcaf5dac5ab6564fc05b8f5381", "path": "关于页面UI优化总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3ec295e8b9bd87044231b865d25e3cb54", "path": "键盘工具栏功能实现总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f381b32ef709deb98f5bfdb09baa8196d8", "path": "盲盒弹窗修改验证.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3fa462a561065e6d5e541fac68ea67f40", "path": "盲盒功能实现总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f34c559feb197eea4fc4f14c35a38313b9", "path": "盲盒功能使用指南.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a08f585de39959cb62c511db035a5e50", "path": "盲盒配置功能验证报告.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3747d858c088a323b0113e62ef5ece467", "path": "盲盒配置与动画集成报告.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f36c225fd6afe9c8369e775f3cc39cfdd2", "path": "盲盒Bug修复报告.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f37a16f8cc72dd08f7ccd65a5c87b4bbdd", "path": "免费用户iCloud同步显示策略.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b34296130666b72bbc8c3087d7f26300", "path": "权限降级处理方案.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f31f24cba55dad5a3fa83fef5bf268b030", "path": "AI分析报告详情页面UI优化总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f326c7931e9978807dfee11dde20fe5219", "path": "AI分析报告页面布局优化报告.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3fb15eed96d7c3287db6fe4188b004a8f", "path": "AI分析功能编译错误修复报告.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f37f439b487c2926ed1fd7e17f647da65d", "path": "AI分析功能实现总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3fc45640eafd3d1ac536ecab36ceb328b", "path": "AI分析内容展示页面重构总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e6a06c2f07e19413aa6777fb7141edd3", "path": "AI分析页面重构总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "a2523ee572d34a1df6925e1e93a0e1f3ae851de721ad62267135d96a04095e40", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c018a2f34a86140a5e8e4afd0f095cc9", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f3ea2dc02a2f23d2803e4d8c2162bff4c8", "path": "iCloud同步功能实现总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e3cc89c509074773b5aa03606506613e", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e4b184142f97bfb2392cdba2b9eb09fd", "path": "Info.plist.reference", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.plist.strings", "guid": "a2523ee572d34a1df6925e1e93a0e1f3091c90b15a4d5bf823b0fa8da59be976", "path": "en.lproj/Localizable.strings", "regionVariantName": "en", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.strings", "guid": "a2523ee572d34a1df6925e1e93a0e1f39cf18a729424bc4e9d9962d576456beb", "path": "zh-Hans.lproj/Localizable.strings", "regionVariantName": "zh-Hans", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f347b7ee48d85244b8b5ad7bf9c923af83", "name": "Localizable.strings", "path": "", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f377f3121d51f0949854199776f44fb850", "path": "Markdown渲染功能实现报告.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3cef4963aadebc4f8abdecd1305222692", "path": "Persistence.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "a2523ee572d34a1df6925e1e93a0e1f370bec21b8f963ebaa0a1825b33b63382", "path": "ztt2.entitlements", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcdatamodel", "guid": "a2523ee572d34a1df6925e1e93a0e1f306ba4c8025a9a979d2291016e1b91350", "path": "ztt2.xcdatamodel", "sourceTree": "<group>", "type": "file"}], "fileType": "wrapper.xcdatamodeld", "guid": "a2523ee572d34a1df6925e1e93a0e1f38ea8b8035fd353046c53b63f982cfa83", "name": "ztt2.xcdatamodeld", "path": "ztt2.xcdatamodeld", "sourceTree": "<group>", "type": "versionGroup"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f38ddf1f906978d01ff1e82fa3c85c3003", "path": "ztt2App.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3f29ab29fce8e6a565e14563d07291ced", "name": "ztt2", "path": "ztt2", "sourceTree": "<group>", "type": "group"}, {"guid": "a2523ee572d34a1df6925e1e93a0e1f3ac5bcebb2167034ea4e1f5470b4c2e93", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f36944541524b1a644404d2e856465bfd1", "name": "ztt2", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "a2523ee572d34a1df6925e1e93a0e1f3", "path": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/我的项目/转团团/ztt2", "targets": ["TARGET@v11_hash=29d8a45022ae6445c90a8ae7cdc73de3"]}