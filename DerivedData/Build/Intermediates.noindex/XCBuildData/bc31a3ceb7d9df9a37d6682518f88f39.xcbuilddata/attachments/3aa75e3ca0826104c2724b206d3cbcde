/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/AddRewardFormView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/CustomRewardExchangeView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCard/Components/ScratchCardResultView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LiquidTabBarBackground.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Services/SpeechRecognitionService.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/AIAnalysisModels.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Extensions/UIApplication+Extensions.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LotteryConfigPopupView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBoxTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Subscription/Components/MembershipContentView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Extensions/View+Extensions.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/AIReportDetailView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberPickerPopupView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberRewardExchangeView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/CardComparisonTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ViewModels/GrowthDiaryViewModel.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBoxIntegrationTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Services/iCloudSyncManager.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/BlindBoxConfigPopupView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ViewModels/HomeViewModel.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ExchangeRecordTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/AIAnalysisView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/About/AboutView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/ActionButtonsView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Subscription/Components/MembershipAgreementView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBoxConfigIntegrationTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/MemberPointsModels.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Profile/Components/SubscriptionBannerSection.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/AIAnalysisHistoryView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Utils/KeychainManager.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/CoreDataExtensions.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Subscription/Components/BackgroundImageLayer.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ViewModels/MemberDetailViewModel.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBoxBugFixTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/MemberDetailView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/WheelConfigPopupView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/DataModelUsageExamples.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ProductIntroductionView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Persistence.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/CustomTabBar.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ProfileView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ztt2App.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyOperationFormView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCardTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/DateRangePickerView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ContentView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/About/Policy/UserAgreementView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberPointsFormView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/SubscriptionTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/DataManager.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/DateRangeType.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/TestPointsFormView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/HomeView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyTotalScoreView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/ScratchCardItem.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/DatePickerView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyOperationOptionsView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberPointsOptionsView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberSelectionPopupView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/VoiceRecordingButton.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/AvatarFixVerificationView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/KeyboardToolbarTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Styles/DesignSystem.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/WheelConfigTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/GrowthDiaryView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/WheelIntegrationTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryConfigTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBox/Components/ExplosionAnimationView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryOptionsTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Profile/Components/iCloudSyncStatusView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/AvatarDisplayTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBox/Components/BlindBoxCubeView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCard/ScratchCardView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/DatePickerPopupView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCard/Components/ScratchCardGridView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Subscription/SubscriptionView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryOptionsView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Styles/iPadLayoutConfig.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Profile/Components/SystemSettingsSection.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/KeyboardToolbarView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Subscription/Components/SubscriptionUserInfoSection.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryWheelTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBox/BlindBoxView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/About/Policy/PrivacyPolicyView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCard/Components/ScratchCardCanvasView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryWheelView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ViewModels/AIAnalysisViewModel.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Extensions/Color+Extensions.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBox/Components/BlindBoxGridView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Subscription/Components/MembershipTabSegment.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCard/ScratchCardStylePreview.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/MainTabView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Profile/Components/UserInfoSection.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Styles/ResponsiveLayoutConfig.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Extensions/DeviceDetection.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/RoleSelectionView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCard/ViewModels/ScratchCardViewModel.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/MemberDetailTestView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LiquidTabBarBubble.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LiquidTabBarShape.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Extensions/String+Localization.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyMemberCardView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LotteryToolSelectionPopupView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/AddMemberFormView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/BlindBoxItem.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/About/Policy/ChildrenPrivacyPolicyView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Utils/KeyboardUtils.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBox/BlindBoxViewModel.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/ScratchCardConfigPopupView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Profile/Components/SubscriptionDowngradeHandler.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Services/AIAnalysisService.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyMemberGridView.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/User+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/User+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/Subscription+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/Subscription+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/GlobalRule+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/GlobalRule+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/Member+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/Member+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/PointRecord+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/PointRecord+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/DiaryEntry+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/DiaryEntry+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/AIReport+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/AIReport+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/MemberRule+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/MemberRule+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/MemberPrize+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/MemberPrize+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/RedemptionRecord+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/RedemptionRecord+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryRecord+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryRecord+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryConfig+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryConfig+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryItem+CoreDataClass.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryItem+CoreDataProperties.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/ztt2+CoreDataModel.swift
/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/GeneratedAssetSymbols.swift
