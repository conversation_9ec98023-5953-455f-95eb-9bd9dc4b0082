{"": {"diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2-master.swiftdeps"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/AIReport+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/AIReport+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReport+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/DiaryEntry+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/DiaryEntry+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DiaryEntry+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/GlobalRule+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/GlobalRule+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GlobalRule+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryConfig+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryConfig+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfig+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryItem+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryItem+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryItem+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryRecord+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryRecord+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryRecord+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/Member+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/Member+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Member+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/MemberPrize+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/MemberPrize+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPrize+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/MemberRule+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/MemberRule+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRule+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/PointRecord+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/PointRecord+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PointRecord+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/RedemptionRecord+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/RedemptionRecord+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RedemptionRecord+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/Subscription+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/Subscription+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Subscription+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/User+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataClass.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/User+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataProperties.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/User+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/ztt2+CoreDataModel.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2+CoreDataModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2+CoreDataModel.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2+CoreDataModel.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2+CoreDataModel.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2+CoreDataModel.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2+CoreDataModel.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2+CoreDataModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2+CoreDataModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ContentView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ContentView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ContentView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ContentView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ContentView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Extensions/Color+Extensions.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Color+Extensions.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Color+Extensions.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Color+Extensions.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Color+Extensions.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Color+Extensions.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Color+Extensions.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Color+Extensions.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Color+Extensions~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Extensions/DeviceDetection.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DeviceDetection.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DeviceDetection.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DeviceDetection.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DeviceDetection.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DeviceDetection.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DeviceDetection.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DeviceDetection.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DeviceDetection~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Extensions/String+Localization.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/String+Localization.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/String+Localization.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/String+Localization.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/String+Localization.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/String+Localization.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/String+Localization.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/String+Localization.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/String+Localization~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Extensions/UIApplication+Extensions.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UIApplication+Extensions.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UIApplication+Extensions.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UIApplication+Extensions.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UIApplication+Extensions.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UIApplication+Extensions.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UIApplication+Extensions.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UIApplication+Extensions.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UIApplication+Extensions~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Extensions/View+Extensions.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/View+Extensions.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/View+Extensions.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/View+Extensions.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/View+Extensions.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/View+Extensions.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/View+Extensions.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/View+Extensions~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/AIAnalysisModels.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisModels.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisModels.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisModels.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisModels.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisModels.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisModels.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisModels.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisModels~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/BlindBoxItem.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxItem.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxItem.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxItem.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxItem.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxItem.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxItem.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxItem.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxItem~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/CoreDataExtensions.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CoreDataExtensions.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CoreDataExtensions.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CoreDataExtensions.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CoreDataExtensions.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CoreDataExtensions.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CoreDataExtensions.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CoreDataExtensions.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CoreDataExtensions~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/DataManager.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataManager.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataManager.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataManager.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataManager.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataManager.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/DataModelUsageExamples.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataModelUsageExamples.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataModelUsageExamples.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataModelUsageExamples.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataModelUsageExamples.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataModelUsageExamples.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataModelUsageExamples.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataModelUsageExamples.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DataModelUsageExamples~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/DateRangeType.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangeType.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangeType.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangeType.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangeType.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangeType.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangeType.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangeType.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangeType~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/MemberPointsModels.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsModels.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsModels.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsModels.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsModels.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsModels.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsModels.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsModels.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsModels~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/ScratchCardItem.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardItem.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardItem.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardItem.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardItem.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardItem.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardItem.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardItem.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardItem~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Persistence.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Persistence.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Persistence.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Persistence.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Persistence.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Persistence.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Persistence.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Persistence.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Persistence~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Services/AIAnalysisService.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisService.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisService.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisService.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisService.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisService.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisService.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisService.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisService~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Services/SpeechRecognitionService.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SpeechRecognitionService.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SpeechRecognitionService.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SpeechRecognitionService.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SpeechRecognitionService.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SpeechRecognitionService.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SpeechRecognitionService.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SpeechRecognitionService.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SpeechRecognitionService~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Services/iCloudSyncManager.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncManager.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncManager.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncManager.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncManager.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncManager.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Styles/DesignSystem.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DesignSystem.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DesignSystem.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DesignSystem.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DesignSystem.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DesignSystem.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DesignSystem.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DesignSystem.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DesignSystem~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Styles/ResponsiveLayoutConfig.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ResponsiveLayoutConfig.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ResponsiveLayoutConfig.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ResponsiveLayoutConfig.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ResponsiveLayoutConfig.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ResponsiveLayoutConfig.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ResponsiveLayoutConfig.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ResponsiveLayoutConfig.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ResponsiveLayoutConfig~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Styles/iPadLayoutConfig.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iPadLayoutConfig.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iPadLayoutConfig.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iPadLayoutConfig.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iPadLayoutConfig.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iPadLayoutConfig.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iPadLayoutConfig.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iPadLayoutConfig.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iPadLayoutConfig~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Utils/KeyboardUtils.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardUtils.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardUtils.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardUtils.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardUtils.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardUtils.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardUtils.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardUtils.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardUtils~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Utils/KeychainManager.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeychainManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeychainManager.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeychainManager.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeychainManager.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeychainManager.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeychainManager.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeychainManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeychainManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ViewModels/AIAnalysisViewModel.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisViewModel.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisViewModel.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisViewModel.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ViewModels/GrowthDiaryViewModel.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryViewModel.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryViewModel.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryViewModel.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ViewModels/HomeViewModel.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeViewModel.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeViewModel.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeViewModel.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ViewModels/MemberDetailViewModel.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailViewModel.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailViewModel.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailViewModel.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/AIAnalysisHistoryView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisHistoryView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisHistoryView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisHistoryView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisHistoryView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisHistoryView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisHistoryView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisHistoryView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisHistoryView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/AIAnalysisView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIAnalysisView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/AIReportDetailView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReportDetailView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReportDetailView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReportDetailView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReportDetailView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReportDetailView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReportDetailView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReportDetailView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AIReportDetailView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/About/AboutView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AboutView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AboutView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AboutView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AboutView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AboutView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AboutView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AboutView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AboutView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/About/Policy/ChildrenPrivacyPolicyView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ChildrenPrivacyPolicyView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ChildrenPrivacyPolicyView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ChildrenPrivacyPolicyView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ChildrenPrivacyPolicyView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ChildrenPrivacyPolicyView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ChildrenPrivacyPolicyView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ChildrenPrivacyPolicyView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ChildrenPrivacyPolicyView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/About/Policy/PrivacyPolicyView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PrivacyPolicyView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PrivacyPolicyView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PrivacyPolicyView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PrivacyPolicyView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PrivacyPolicyView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PrivacyPolicyView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PrivacyPolicyView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/PrivacyPolicyView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/About/Policy/UserAgreementView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserAgreementView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserAgreementView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserAgreementView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserAgreementView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserAgreementView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserAgreementView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserAgreementView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserAgreementView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/AvatarDisplayTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarDisplayTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarDisplayTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarDisplayTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarDisplayTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarDisplayTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarDisplayTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarDisplayTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarDisplayTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/AvatarFixVerificationView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarFixVerificationView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarFixVerificationView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarFixVerificationView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarFixVerificationView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarFixVerificationView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarFixVerificationView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarFixVerificationView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AvatarFixVerificationView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBox/BlindBoxView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBox/BlindBoxViewModel.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxViewModel.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxViewModel.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxViewModel.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBox/Components/BlindBoxCubeView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxCubeView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxCubeView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxCubeView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxCubeView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxCubeView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxCubeView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxCubeView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxCubeView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBox/Components/BlindBoxGridView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxGridView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxGridView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxGridView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxGridView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxGridView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxGridView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxGridView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxGridView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBox/Components/ExplosionAnimationView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExplosionAnimationView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExplosionAnimationView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExplosionAnimationView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExplosionAnimationView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExplosionAnimationView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExplosionAnimationView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExplosionAnimationView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExplosionAnimationView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBoxBugFixTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxBugFixTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxBugFixTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxBugFixTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxBugFixTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxBugFixTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxBugFixTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxBugFixTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxBugFixTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBoxConfigIntegrationTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigIntegrationTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigIntegrationTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigIntegrationTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigIntegrationTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigIntegrationTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigIntegrationTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigIntegrationTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigIntegrationTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBoxIntegrationTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxIntegrationTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxIntegrationTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxIntegrationTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxIntegrationTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxIntegrationTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxIntegrationTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxIntegrationTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxIntegrationTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/BlindBoxTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/CardComparisonTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CardComparisonTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CardComparisonTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CardComparisonTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CardComparisonTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CardComparisonTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CardComparisonTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CardComparisonTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CardComparisonTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/ActionButtonsView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ActionButtonsView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ActionButtonsView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ActionButtonsView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ActionButtonsView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ActionButtonsView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ActionButtonsView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ActionButtonsView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ActionButtonsView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/AddMemberFormView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddMemberFormView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddMemberFormView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddMemberFormView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddMemberFormView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddMemberFormView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddMemberFormView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddMemberFormView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddMemberFormView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/AddRewardFormView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddRewardFormView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddRewardFormView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddRewardFormView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddRewardFormView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddRewardFormView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddRewardFormView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddRewardFormView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/AddRewardFormView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/BlindBoxConfigPopupView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigPopupView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigPopupView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigPopupView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigPopupView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigPopupView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigPopupView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigPopupView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BlindBoxConfigPopupView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/CustomRewardExchangeView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomRewardExchangeView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomRewardExchangeView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomRewardExchangeView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomRewardExchangeView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomRewardExchangeView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomRewardExchangeView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomRewardExchangeView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomRewardExchangeView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/CustomTabBar.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomTabBar.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomTabBar.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomTabBar.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomTabBar.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomTabBar.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomTabBar.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomTabBar.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/CustomTabBar~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/DatePickerPopupView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerPopupView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerPopupView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerPopupView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerPopupView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerPopupView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerPopupView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerPopupView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerPopupView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/DatePickerView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DatePickerView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/DateRangePickerView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangePickerView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangePickerView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangePickerView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangePickerView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangePickerView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangePickerView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangePickerView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/DateRangePickerView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyMemberCardView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberCardView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberCardView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberCardView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberCardView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberCardView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberCardView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberCardView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberCardView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyMemberGridView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberGridView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberGridView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberGridView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberGridView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberGridView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberGridView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberGridView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyMemberGridView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyOperationFormView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationFormView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationFormView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationFormView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationFormView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationFormView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationFormView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationFormView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationFormView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyOperationOptionsView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationOptionsView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationOptionsView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationOptionsView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationOptionsView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationOptionsView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationOptionsView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationOptionsView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyOperationOptionsView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyTotalScoreView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyTotalScoreView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyTotalScoreView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyTotalScoreView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyTotalScoreView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyTotalScoreView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyTotalScoreView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyTotalScoreView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/FamilyTotalScoreView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/KeyboardToolbarView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LiquidTabBarBackground.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBackground.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBackground.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBackground.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBackground.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBackground.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBackground.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBackground.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBackground~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LiquidTabBarBubble.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBubble.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBubble.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBubble.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBubble.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBubble.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBubble.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBubble.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarBubble~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LiquidTabBarShape.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarShape.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarShape.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarShape.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarShape.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarShape.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarShape.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarShape.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LiquidTabBarShape~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LotteryConfigPopupView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigPopupView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigPopupView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigPopupView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigPopupView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigPopupView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigPopupView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigPopupView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigPopupView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LotteryToolSelectionPopupView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryToolSelectionPopupView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryToolSelectionPopupView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryToolSelectionPopupView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryToolSelectionPopupView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryToolSelectionPopupView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryToolSelectionPopupView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryToolSelectionPopupView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryToolSelectionPopupView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberPickerPopupView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPickerPopupView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPickerPopupView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPickerPopupView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPickerPopupView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPickerPopupView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPickerPopupView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPickerPopupView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPickerPopupView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberPointsFormView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsFormView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsFormView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsFormView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsFormView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsFormView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsFormView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsFormView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsFormView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberPointsOptionsView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsOptionsView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsOptionsView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsOptionsView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsOptionsView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsOptionsView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsOptionsView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsOptionsView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberPointsOptionsView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberRewardExchangeView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRewardExchangeView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRewardExchangeView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRewardExchangeView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRewardExchangeView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRewardExchangeView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRewardExchangeView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRewardExchangeView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberRewardExchangeView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberSelectionPopupView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberSelectionPopupView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberSelectionPopupView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberSelectionPopupView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberSelectionPopupView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberSelectionPopupView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberSelectionPopupView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberSelectionPopupView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberSelectionPopupView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/RoleSelectionView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RoleSelectionView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RoleSelectionView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RoleSelectionView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RoleSelectionView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RoleSelectionView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RoleSelectionView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RoleSelectionView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/RoleSelectionView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/ScratchCardConfigPopupView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardConfigPopupView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardConfigPopupView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardConfigPopupView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardConfigPopupView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardConfigPopupView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardConfigPopupView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardConfigPopupView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardConfigPopupView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/VoiceRecordingButton.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/VoiceRecordingButton.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/VoiceRecordingButton.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/VoiceRecordingButton.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/VoiceRecordingButton.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/VoiceRecordingButton.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/VoiceRecordingButton.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/VoiceRecordingButton.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/VoiceRecordingButton~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/WheelConfigPopupView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigPopupView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigPopupView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigPopupView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigPopupView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigPopupView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigPopupView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigPopupView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigPopupView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ExchangeRecordTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExchangeRecordTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExchangeRecordTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExchangeRecordTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExchangeRecordTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExchangeRecordTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExchangeRecordTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExchangeRecordTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ExchangeRecordTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/GrowthDiaryView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/GrowthDiaryView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/HomeView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/HomeView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/KeyboardToolbarTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/KeyboardToolbarTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryConfigTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryConfigTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryOptionsTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryOptionsView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryOptionsView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryWheelTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryWheelView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/LotteryWheelView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/MainTabView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MainTabView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MainTabView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MainTabView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MainTabView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MainTabView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MainTabView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MainTabView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/MemberDetailTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/MemberDetailView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MemberDetailView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ProductIntroductionView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProductIntroductionView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProductIntroductionView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProductIntroductionView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProductIntroductionView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProductIntroductionView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProductIntroductionView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProductIntroductionView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProductIntroductionView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Profile/Components/SubscriptionBannerSection.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionBannerSection.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionBannerSection.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionBannerSection.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionBannerSection.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionBannerSection.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionBannerSection.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionBannerSection.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionBannerSection~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Profile/Components/SubscriptionDowngradeHandler.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionDowngradeHandler.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionDowngradeHandler.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionDowngradeHandler.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionDowngradeHandler.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionDowngradeHandler.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionDowngradeHandler.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionDowngradeHandler.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionDowngradeHandler~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Profile/Components/SystemSettingsSection.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SystemSettingsSection.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SystemSettingsSection.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SystemSettingsSection.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SystemSettingsSection.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SystemSettingsSection.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SystemSettingsSection.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SystemSettingsSection.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SystemSettingsSection~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Profile/Components/UserInfoSection.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserInfoSection.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserInfoSection.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserInfoSection.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserInfoSection.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserInfoSection.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserInfoSection.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserInfoSection.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/UserInfoSection~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Profile/Components/iCloudSyncStatusView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncStatusView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncStatusView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncStatusView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncStatusView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncStatusView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncStatusView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncStatusView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/iCloudSyncStatusView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ProfileView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProfileView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProfileView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProfileView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProfileView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProfileView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProfileView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ProfileView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCard/Components/ScratchCardCanvasView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardCanvasView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardCanvasView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardCanvasView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardCanvasView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardCanvasView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardCanvasView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardCanvasView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardCanvasView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCard/Components/ScratchCardGridView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardGridView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardGridView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardGridView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardGridView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardGridView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardGridView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardGridView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardGridView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCard/Components/ScratchCardResultView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardResultView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardResultView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardResultView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardResultView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardResultView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardResultView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardResultView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardResultView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCard/ScratchCardStylePreview.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardStylePreview.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardStylePreview.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardStylePreview.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardStylePreview.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardStylePreview.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardStylePreview.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardStylePreview.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardStylePreview~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCard/ScratchCardView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCard/ViewModels/ScratchCardViewModel.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardViewModel.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardViewModel.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardViewModel.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ScratchCardTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ScratchCardTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Subscription/Components/BackgroundImageLayer.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BackgroundImageLayer.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BackgroundImageLayer.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BackgroundImageLayer.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BackgroundImageLayer.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BackgroundImageLayer.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BackgroundImageLayer.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BackgroundImageLayer.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/BackgroundImageLayer~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Subscription/Components/MembershipAgreementView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipAgreementView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipAgreementView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipAgreementView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipAgreementView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipAgreementView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipAgreementView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipAgreementView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipAgreementView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Subscription/Components/MembershipContentView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipContentView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipContentView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipContentView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipContentView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipContentView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipContentView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Subscription/Components/MembershipTabSegment.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipTabSegment.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipTabSegment.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipTabSegment.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipTabSegment.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipTabSegment.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipTabSegment.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipTabSegment.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/MembershipTabSegment~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Subscription/Components/SubscriptionUserInfoSection.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionUserInfoSection.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionUserInfoSection.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionUserInfoSection.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionUserInfoSection.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionUserInfoSection.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionUserInfoSection.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionUserInfoSection.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionUserInfoSection~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Subscription/SubscriptionView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/SubscriptionTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/SubscriptionTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/TestPointsFormView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/TestPointsFormView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/TestPointsFormView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/TestPointsFormView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/TestPointsFormView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/TestPointsFormView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/TestPointsFormView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/TestPointsFormView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/TestPointsFormView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/WheelConfigTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelConfigTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/WheelIntegrationTestView.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelIntegrationTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelIntegrationTestView.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelIntegrationTestView.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelIntegrationTestView.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelIntegrationTestView.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelIntegrationTestView.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelIntegrationTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/WheelIntegrationTestView~partial.swiftmodule"}, "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ztt2App.swift": {"const-values": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2App.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2App.d", "diagnostics": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2App.dia", "index-unit-output-path": "/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2App.o", "llvm-bc": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2App.bc", "object": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2App.o", "swift-dependencies": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2App.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/DerivedData/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/ztt2App~partial.swiftmodule"}}