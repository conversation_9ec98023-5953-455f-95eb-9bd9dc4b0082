//
//  KeyboardToolbarTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import SwiftUI

/**
 * 键盘工具栏测试视图
 * 用于测试键盘工具栏功能是否正常工作
 */
struct KeyboardToolbarTestView: View {
    
    @State private var textField1: String = ""
    @State private var textField2: String = ""
    @State private var textEditor: String = ""
    @State private var numberField: String = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 标题
                    Text("键盘工具栏测试")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .padding(.top)
                    
                    Text("点击任意输入框，键盘弹出后应在右上角显示收回键盘按钮")
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                    
                    // 测试输入框组
                    VStack(spacing: 16) {
                        // 普通文本输入框
                        VStack(alignment: .leading, spacing: 8) {
                            Text("普通文本输入框")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            TextField("请输入文本", text: $textField1)
                                .textFieldStyle(TestTextFieldStyle())
                        }
                        
                        // 数字输入框
                        VStack(alignment: .leading, spacing: 8) {
                            Text("数字输入框")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            TextField("请输入数字", text: $numberField)
                                .keyboardType(.numberPad)
                                .textFieldStyle(TestTextFieldStyle())
                        }
                        
                        // 邮箱输入框
                        VStack(alignment: .leading, spacing: 8) {
                            Text("邮箱输入框")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            TextField("请输入邮箱", text: $textField2)
                                .keyboardType(.emailAddress)
                                .textFieldStyle(TestTextFieldStyle())
                        }
                        
                        // 多行文本编辑器
                        VStack(alignment: .leading, spacing: 8) {
                            Text("多行文本编辑器")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            TextEditor(text: $textEditor)
                                .frame(height: 120)
                                .padding(8)
                                .background(Color.white)
                                .cornerRadius(8)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                )
                        }
                    }
                    .padding(.horizontal, 20)
                    
                    // 测试说明
                    VStack(alignment: .leading, spacing: 12) {
                        Text("测试说明：")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            TestInstructionRow(
                                number: "1",
                                instruction: "点击任意输入框，键盘应该弹出"
                            )
                            
                            TestInstructionRow(
                                number: "2", 
                                instruction: "键盘弹出后，在屏幕右上角应显示键盘收回按钮"
                            )
                            
                            TestInstructionRow(
                                number: "3",
                                instruction: "点击收回按钮，键盘应该收起"
                            )
                            
                            TestInstructionRow(
                                number: "4",
                                instruction: "按钮应使用jianpan.png图标"
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                    .background(Color(hex: "#f8ffe5"))
                    .cornerRadius(12)
                    .padding(.horizontal, 20)
                    
                    Spacer(minLength: 100)
                }
            }
            .navigationTitle("键盘工具栏测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .keyboardToolbar() // 添加键盘工具栏
    }
}

/**
 * 测试用的文本框样式
 */
struct TestTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(Color.white)
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
    }
}

/**
 * 测试说明行组件
 */
struct TestInstructionRow: View {
    let number: String
    let instruction: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text(number)
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(Color(hex: "#a9d051"))
                .clipShape(Circle())
            
            Text(instruction)
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .fixedSize(horizontal: false, vertical: true)
            
            Spacer()
        }
    }
}

// MARK: - Preview
#Preview {
    KeyboardToolbarTestView()
}
