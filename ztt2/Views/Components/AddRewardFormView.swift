//
//  AddRewardFormView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 添加奖品表单弹窗组件
 * 基于MemberPointsFormView设计，支持批量添加奖品
 *
 * ## 功能特性
 * - 支持多行表单输入
 * - 动态添加删除表单项
 * - 表单验证和错误提示
 * - 美观的动画效果和触觉反馈
 *
 * ## 设计模式
 * 采用MVVM模式，分离视图和数据逻辑
 *
 * ## 兼容性
 * - iOS 15.6+
 * - 支持深色模式和动态字体
 * - 完整的无障碍支持
 */
struct AddRewardFormView: View {

    // MARK: - Properties
    @Binding var isPresented: Bool
    let onSubmit: (MemberRewardFormData) -> Void
    let onCancel: () -> Void

    // MARK: - State
    @State private var formData = MemberRewardFormData()
    @State private var animationTrigger = false
    @State private var showValidationErrors = false
    @State private var validationResult: MemberRewardFormValidationResult?
    @State private var isSubmitting = false
    @FocusState private var focusedField: UUID?
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        dismissKeyboard()
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            onCancel()
                        }
                    }
                    .transition(.opacity)
                
                // 表单对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        headerView
                        
                        // 分隔线
                        dividerView
                        
                        // 表单区域
                        formSection
                        
                        // 分隔线
                        dividerView
                        
                        // 底部按钮区域
                        buttonsSection
                    }
                    .frame(maxWidth: min(geometry.size.width - 30, 380))
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color(hex: "#B5E36B").opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
                
                // 自动聚焦到第一个输入框
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                    if !formData.items.isEmpty {
                        focusedField = formData.items[0].id
                    }
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
                dismissKeyboard()
            }
        }
        .keyboardToolbar()
    }
    
    // MARK: - 子视图组件
    
    /**
     * 标题栏
     */
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("添加奖品")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("为成员创建专属奖品列表")
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            Spacer()

            // 关闭按钮
            Button(action: {
                dismissKeyboard()
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onCancel()
                }
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
        .padding(.bottom, 16)
    }
    
    /**
     * 分隔线
     */
    private var dividerView: some View {
        Rectangle()
            .fill(Color(hex: "#edf5d9"))
            .frame(height: 1)
            .padding(.horizontal, 20)
    }
    
    /**
     * 表单区域
     */
    private var formSection: some View {
        VStack(spacing: 0) {
            // 表单标题和添加按钮
            HStack {
                Text("奖品信息")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                // 添加按钮
                if formData.canAddMoreItems {
                    Button(action: {
                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                        impactFeedback.impactOccurred()

                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            formData.addItem()
                        }

                        // 聚焦到新添加的项
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            focusedField = formData.items.last?.id
                        }
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 20))
                            .foregroundColor(Color(hex: "#B5E36B"))
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            .padding(.bottom, 12)

            // 表单内容区域
            ScrollView(.vertical, showsIndicators: true) {
                VStack(spacing: 16) {
                    ForEach(formData.items.indices, id: \.self) { index in
                        RewardFormRow(
                            item: $formData.items[index],
                            index: index,
                            canDelete: formData.items.count > 1,
                            focusedField: $focusedField,
                            onDelete: {
                                removeRewardItem(at: index)
                            }
                        )
                        .disabled(isSubmitting)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(maxHeight: 300)
            .contentShape(Rectangle())
            .onTapGesture {
                dismissKeyboard()
            }

            // 验证错误显示
            if showValidationErrors, let result = validationResult, !result.isValid {
                validationErrorsView
            }
        }
    }

    /**
     * 验证错误显示
     */
    private var validationErrorsView: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 8) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.errorColor)

                Text("请检查以下问题：")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.errorColor)
            }

            if let result = validationResult {
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(result.errorMessages, id: \.self) { errorMessage in
                        Text("• \(errorMessage)")
                            .font(.system(size: 12))
                            .foregroundColor(DesignSystem.Colors.errorColor)
                    }
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(DesignSystem.Colors.errorColor.opacity(0.1))
        .cornerRadius(8)
        .padding(.horizontal, 20)
        .padding(.bottom, 16)
    }

    /**
     * 底部按钮
     */
    private var buttonsSection: some View {
        HStack(spacing: 12) {
            // 取消按钮
            Button(action: {
                dismissKeyboard()
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onCancel()
                }
            }) {
                Text("取消")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }

            // 确认按钮
            Button(action: {
                submitForm()
            }) {
                HStack(spacing: 8) {
                    if isSubmitting {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    }

                    Text(isSubmitting ? "添加中..." : "添加奖品")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 44)
                .background(
                    formData.hasValidData && !isSubmitting
                    ? Color(hex: "#B5E36B")
                    : Color.gray.opacity(0.3)
                )
                .cornerRadius(8)
            }
            .disabled(!formData.hasValidData || isSubmitting)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }

    // MARK: - 辅助方法

    /**
     * 隐藏键盘
     */
    private func dismissKeyboard() {
        focusedField = nil
    }

    /**
     * 提交表单
     */
    private func submitForm() {
        dismissKeyboard()

        // 批量验证
        let result = formData.items.validateBatch()
        validationResult = result

        if result.isValid {
            isSubmitting = true

            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()

            // 延迟提交，模拟处理时间
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                onSubmit(formData)
                isSubmitting = false
            }
        } else {
            // 显示验证错误
            let _ = withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                showValidationErrors = true
            }

            // 错误触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
        }
    }

    /**
     * 移除奖品项
     */
    private func removeRewardItem(at index: Int) {
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            formData.removeItem(at: index)
        }
    }
}

/**
 * 添加奖品表单文本框样式
 */
struct AddRewardTextFieldStyle: TextFieldStyle {
    let minHeight: CGFloat

    init(minHeight: CGFloat = 44) {
        self.minHeight = minHeight
    }

    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .frame(minHeight: minHeight)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(hex: "#B5E36B").opacity(0.3), lineWidth: 1)
            )
    }
}

// MARK: - 奖品表单行组件
struct RewardFormRow: View {
    @Binding var item: MemberRewardFormData.RewardFormItem
    let index: Int
    let canDelete: Bool
    @FocusState.Binding var focusedField: UUID?
    let onDelete: () -> Void

    var body: some View {
        VStack(spacing: 12) {
            // 行标题和删除按钮
            HStack {
                Text("奖品 \(index + 1)")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                if canDelete {
                    Button(action: onDelete) {
                        Image(systemName: "minus.circle.fill")
                            .font(.system(size: 18))
                            .foregroundColor(Color.red.opacity(0.7))
                    }
                }
            }

            // 输入字段
            HStack(spacing: 12) {
                // 奖品名称
                VStack(alignment: .leading, spacing: 4) {
                    Text("名称 *")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    TextField("奖品名称", text: $item.name)
                        .textFieldStyle(AddRewardTextFieldStyle())
                        .focused($focusedField, equals: item.id)
                }

                // 积分消耗
                VStack(alignment: .leading, spacing: 4) {
                    Text("积分 *")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    TextField("积分", text: $item.pointsCost)
                        .textFieldStyle(AddRewardTextFieldStyle())
                        .keyboardType(.numberPad)
                        .frame(width: 80)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(8)
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()

        AddRewardFormView(
            isPresented: .constant(true),
            onSubmit: { formData in
                print("提交奖品数量: \(formData.validItems.count)")
            },
            onCancel: {
                print("取消添加奖品")
            }
        )
    }
}
