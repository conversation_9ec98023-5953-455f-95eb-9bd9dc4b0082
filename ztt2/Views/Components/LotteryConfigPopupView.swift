//
//  LotteryConfigPopupView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 抽奖道具配置弹窗基础组件
 * 提供通用的弹窗样式、动画效果和基础布局结构
 */
struct LotteryConfigPopupView<Content: View>: View {
    
    @Binding var isPresented: Bool
    let title: String
    let onSave: () -> Void
    let onCancel: () -> Void
    let content: () -> Content
    
    @State private var animationTrigger = false
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            onCancel()
                        }
                    }
                    .transition(.opacity)
                
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        HStack {
                            Text(title)
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            Spacer()
                            
                            Button(action: {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    onCancel()
                                }
                            }) {
                                Image(systemName: "xmark")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                                    .frame(width: 24, height: 24)
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                        .background(Color.white)
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                        
                        // 内容区域
                        ScrollView {
                            content()
                                .padding(.horizontal, 20)
                                .padding(.vertical, 16)
                        }
                        .background(Color.white)
                        
                        // 分隔线
                        Rectangle()
                            .fill(Color(hex: "#edf5d9"))
                            .frame(height: 1)
                        
                        // 底部按钮区域
                        HStack(spacing: 12) {
                            // 取消按钮
                            Button(action: {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    onCancel()
                                }
                            }) {
                                Text("lottery_config.button.cancel".localized)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 44)
                                    .background(Color(hex: "#f5f5f5"))
                                    .cornerRadius(8)
                            }
                            
                            // 保存按钮
                            Button(action: {
                                onSave()
                            }) {
                                Text("lottery_config.button.save".localized)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 44)
                                    .background(Color(hex: "#a9d051"))
                                    .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                        .background(Color.white)
                    }
                    .background(Color.white)
                    .cornerRadius(16)
                    .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
                    .frame(maxWidth: min(geometry.size.width - 40, 400))
                    .frame(maxHeight: geometry.size.height * 0.85)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                    .scaleEffect(animationTrigger ? 1.0 : 0.8)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .animation(.spring(response: 0.5, dampingFraction: 0.8), value: animationTrigger)
                }
            }
        }
        .keyboardToolbar()
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
    }
}

/**
 * 配置项输入组件
 * 用于统一的输入框样式
 */
struct ConfigInputField: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    let keyboardType: UIKeyboardType
    
    init(title: String, placeholder: String, text: Binding<String>, keyboardType: UIKeyboardType = .default) {
        self.title = title
        self.placeholder = placeholder
        self._text = text
        self.keyboardType = keyboardType
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            TextField(placeholder, text: $text)
                .font(.system(size: 16))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .keyboardType(keyboardType)
                .padding(.horizontal, 12)
                .padding(.vertical, 10)
                .background(Color(hex: "#f8ffe5"))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color(hex: "#a9d051").opacity(0.3), lineWidth: 1)
                )
        }
    }
}

/**
 * 数量选择器组件
 * 用于选择道具数量
 */
struct CountSelectorView: View {
    let title: String
    let range: String
    @Binding var count: Int
    let minCount: Int
    let maxCount: Int
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                Text(range)
                    .font(.system(size: 12))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            HStack {
                // 减少按钮
                Button(action: {
                    if count > minCount {
                        count -= 1
                    }
                }) {
                    Image(systemName: "minus")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(count > minCount ? Color(hex: "#a9d051") : DesignSystem.Colors.textTertiary)
                        .frame(width: 32, height: 32)
                        .background(Color(hex: "#f8ffe5"))
                        .cornerRadius(6)
                }
                .disabled(count <= minCount)
                
                Spacer()
                
                // 当前数量显示
                Text("\(count)")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .frame(minWidth: 40)
                
                Spacer()
                
                // 增加按钮
                Button(action: {
                    if count < maxCount {
                        count += 1
                    }
                }) {
                    Image(systemName: "plus")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(count < maxCount ? Color(hex: "#a9d051") : DesignSystem.Colors.textTertiary)
                        .frame(width: 32, height: 32)
                        .background(Color(hex: "#f8ffe5"))
                        .cornerRadius(6)
                }
                .disabled(count >= maxCount)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(hex: "#f8ffe5"))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(hex: "#a9d051").opacity(0.3), lineWidth: 1)
            )
        }
    }
}

#Preview {
    LotteryConfigPopupView(
        isPresented: .constant(true),
        title: "大转盘配置",
        onSave: { print("保存") },
        onCancel: { print("取消") }
    ) {
        VStack(spacing: 16) {
            ConfigInputField(
                title: "每次消耗积分",
                placeholder: "请输入积分",
                text: .constant("10"),
                keyboardType: .numberPad
            )
            
            CountSelectorView(
                title: "分区数量",
                range: "4-12个分区",
                count: .constant(8),
                minCount: 4,
                maxCount: 12
            )
        }
    }
}
