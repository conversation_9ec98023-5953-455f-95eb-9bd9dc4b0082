//
//  VoiceRecordingButton.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import SwiftUI

/**
 * 语音录制按钮组件
 * 支持按住录音的交互，带有动画效果
 */
struct VoiceRecordingButton: View {
    
    // MARK: - Properties
    @ObservedObject var speechService: SpeechRecognitionService
    let onRecognizedText: (String) -> Void
    
    // MARK: - State
    @State private var isPressed = false
    @State private var scale: CGFloat = 1.0
    @State private var pulseScale: CGFloat = 1.0
    @State private var showPermissionAlert = false
    
    // MARK: - Constants
    private let buttonSize: CGFloat = 60
    private let iconSize: CGFloat = 28
    
    var body: some View {
        ZStack {
            // 外圈脉冲效果（录音时显示）
            if speechService.isRecording {
                Circle()
                    .stroke(Color(hex: "#B5E36B").opacity(0.3), lineWidth: 2)
                    .frame(width: buttonSize * pulseScale, height: buttonSize * pulseScale)
                    .scaleEffect(pulseScale)
                    .opacity(2.0 - pulseScale)
                    .animation(
                        Animation.easeInOut(duration: 1.0)
                            .repeatForever(autoreverses: false),
                        value: pulseScale
                    )
            }
            
            // 主按钮
            Button(action: {}) {
                ZStack {
                    // 背景圆圈
                    Circle()
                        .fill(
                            speechService.isRecording 
                                ? Color(hex: "#FF6B6B") 
                                : Color(hex: "#B5E36B")
                        )
                        .frame(width: buttonSize, height: buttonSize)
                        .scaleEffect(scale)
                        .shadow(
                            color: speechService.isRecording 
                                ? Color(hex: "#FF6B6B").opacity(0.3)
                                : Color(hex: "#B5E36B").opacity(0.3),
                            radius: 8,
                            x: 0,
                            y: 4
                        )
                    
                    // 图标
                    Image(systemName: speechService.isRecording ? "stop.fill" : "mic.fill")
                        .font(.system(size: iconSize, weight: .medium))
                        .foregroundColor(.white)
                        .scaleEffect(speechService.isRecording ? 0.8 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: speechService.isRecording)
                }
            }
            .buttonStyle(PlainButtonStyle())
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
            .simultaneousGesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { _ in
                        if !isPressed {
                            isPressed = true
                            startRecording()
                        }
                    }
                    .onEnded { _ in
                        isPressed = false
                        stopRecording()
                    }
            )
        }
        .onAppear {
            if speechService.isRecording {
                startPulseAnimation()
            }
        }
        .onChange(of: speechService.isRecording) { isRecording in
            if isRecording {
                startPulseAnimation()
                withAnimation(.easeInOut(duration: 0.2)) {
                    scale = 1.1
                }
            } else {
                stopPulseAnimation()
                withAnimation(.easeInOut(duration: 0.2)) {
                    scale = 1.0
                }
                
                // 录音结束时，如果有识别到的文本，则回调
                if !speechService.recognizedText.isEmpty {
                    onRecognizedText(speechService.recognizedText)
                    speechService.clearRecognizedText()
                }
            }
        }
        .onChange(of: speechService.errorMessage) { errorMessage in
            if let error = errorMessage, !error.isEmpty {
                if error.contains("权限") {
                    showPermissionAlert = true
                }
            }
        }
        .alert("权限请求", isPresented: $showPermissionAlert) {
            Button("去设置") {
                openAppSettings()
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("需要麦克风和语音识别权限才能使用语音转文字功能，请在设置中开启相关权限。")
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 开始录音
     */
    private func startRecording() {
        guard speechService.isAuthorized else {
            speechService.requestPermissions()
            return
        }
        
        speechService.startRecording()
    }
    
    /**
     * 停止录音
     */
    private func stopRecording() {
        speechService.stopRecording()
    }
    
    /**
     * 开始脉冲动画
     */
    private func startPulseAnimation() {
        pulseScale = 1.0
        withAnimation(
            Animation.easeInOut(duration: 1.0)
                .repeatForever(autoreverses: false)
        ) {
            pulseScale = 1.5
        }
    }
    
    /**
     * 停止脉冲动画
     */
    private func stopPulseAnimation() {
        withAnimation(.easeInOut(duration: 0.3)) {
            pulseScale = 1.0
        }
    }
    
    /**
     * 打开应用设置
     */
    private func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 40) {
        Text("语音录制按钮测试")
            .font(.title2)
            .padding()
        
        VoiceRecordingButton(
            speechService: SpeechRecognitionService()
        ) { recognizedText in
            print("识别到的文本: \(recognizedText)")
        }
        
        Spacer()
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(Color.gray.opacity(0.1))
}
