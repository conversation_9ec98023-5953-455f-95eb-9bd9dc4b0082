//
//  VoiceRecordingButton.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import SwiftUI

/**
 * 语音录制按钮组件
 * 支持按住录音的交互，带有动画效果
 */
struct VoiceRecordingButton: View {
    
    // MARK: - Properties
    @ObservedObject var speechService: SpeechRecognitionService
    let onRecognizedText: (String) -> Void
    
    // MARK: - State
    @State private var isPressed = false
    @State private var scale: CGFloat = 1.0
    @State private var pulseScale: CGFloat = 1.0
    @State private var showPermissionAlert = false
    
    // MARK: - Constants
    private let buttonSize: CGFloat = 80  // 使用与原来融球相同的大小
    private let iconSize: CGFloat = 35    // 使用与原来图标相同的大小
    
    var body: some View {
        ZStack {
            // 外圈脉冲效果（录音时显示）
            if speechService.isRecording {
                Circle()
                    .stroke(Color(hex: "#B5E36B").opacity(0.3), lineWidth: 2)
                    .frame(width: buttonSize * pulseScale, height: buttonSize * pulseScale)
                    .scaleEffect(pulseScale)
                    .opacity(2.0 - pulseScale)
                    .animation(
                        Animation.easeInOut(duration: 1.0)
                            .repeatForever(autoreverses: false),
                        value: pulseScale
                    )
            }
            
            // 主按钮
            Button(action: {}) {
                ZStack {
                    // 背景圆圈 - 使用原来的黄色样式
                    Circle()
                        .fill(
                            speechService.isRecording
                                ? Color(hex: "#FF6B6B")  // 录音时变红
                                : Color(hex: "#FFE49E")  // 默认使用原来的黄色
                        )
                        .frame(width: buttonSize, height: buttonSize)
                        .scaleEffect(scale)
                        .shadow(
                            color: speechService.isRecording
                                ? Color(hex: "#FF6B6B").opacity(0.3)
                                : Color(hex: "#FFE49E").opacity(0.3),
                            radius: 8,
                            x: 0,
                            y: 4
                        )
                    
                    // 图标 - 使用原来的录音图标和颜色
                    if speechService.isRecording {
                        Image(systemName: "stop.fill")
                            .font(.system(size: iconSize * 0.8, weight: .medium))
                            .foregroundColor(.white)
                    } else {
                        Image("录音")  // 使用原来的录音图标
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: iconSize * 1.3, height: iconSize * 1.3)  // 与原来LiquidTabBarBubble中的大小一致
                            .foregroundColor(Color(hex: "#666666"))  // 使用原来的图标颜色
                    }
                }
            }
            .buttonStyle(PlainButtonStyle())
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
            .simultaneousGesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { _ in
                        if !isPressed {
                            isPressed = true
                            startRecording()
                        }
                    }
                    .onEnded { _ in
                        isPressed = false
                        stopRecording()
                    }
            )
        }
        .onAppear {
            if speechService.isRecording {
                startPulseAnimation()
            }
        }
        .onChange(of: speechService.isRecording) { isRecording in
            if isRecording {
                startPulseAnimation()
                withAnimation(.easeInOut(duration: 0.2)) {
                    scale = 1.1
                }
            } else {
                stopPulseAnimation()
                withAnimation(.easeInOut(duration: 0.2)) {
                    scale = 1.0
                }
                
                // 录音结束时，如果有识别到的文本，则回调
                if !speechService.recognizedText.isEmpty {
                    onRecognizedText(speechService.recognizedText)
                    speechService.clearRecognizedText()
                }
            }
        }
        .onChange(of: speechService.errorMessage) { errorMessage in
            if let error = errorMessage, !error.isEmpty {
                if error.contains("权限") {
                    showPermissionAlert = true
                }
            }
        }
        .alert("权限请求", isPresented: $showPermissionAlert) {
            Button("去设置") {
                openAppSettings()
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("需要麦克风和语音识别权限才能使用语音转文字功能，请在设置中开启相关权限。")
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 开始录音
     */
    private func startRecording() {
        guard speechService.isAuthorized else {
            speechService.requestPermissions()
            return
        }
        
        speechService.startRecording()
    }
    
    /**
     * 停止录音
     */
    private func stopRecording() {
        speechService.stopRecording()
    }
    
    /**
     * 开始脉冲动画
     */
    private func startPulseAnimation() {
        pulseScale = 1.0
        withAnimation(
            Animation.easeInOut(duration: 1.0)
                .repeatForever(autoreverses: false)
        ) {
            pulseScale = 1.5
        }
    }
    
    /**
     * 停止脉冲动画
     */
    private func stopPulseAnimation() {
        withAnimation(.easeInOut(duration: 0.3)) {
            pulseScale = 1.0
        }
    }
    
    /**
     * 打开应用设置
     */
    private func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 40) {
        Text("语音录制按钮测试")
            .font(.title2)
            .padding()
        
        VoiceRecordingButton(
            speechService: SpeechRecognitionService()
        ) { recognizedText in
            print("识别到的文本: \(recognizedText)")
        }
        
        Spacer()
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(Color.gray.opacity(0.1))
}
