import SwiftUI

/**
 * 键盘工具栏视图
 * 提供在键盘右上角显示一个收起键盘按钮的功能
 */
struct KeyboardToolbarView: View {
    var body: some View {
        Button(action: {
            UIApplication.shared.hideKeyboard()
        }) {
            Image("jianpan")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 30, height: 30)
                .foregroundColor(.primary)
                .padding(8)
        }
        .background(Color(.systemGray5).opacity(0.8))
        .cornerRadius(8)
    }
}

/**
 * 键盘工具栏修饰符
 * 为任何视图添加键盘工具栏功能
 */
struct KeyboardToolbarModifier: ViewModifier {
    @State private var keyboardHeight: CGFloat = 0
    
    func body(content: Content) -> some View {
        ZStack {
            content
            
            if keyboardHeight > 0 {
                GeometryReader { geometry in
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            KeyboardToolbarView()
                                .padding(.trailing, 10)
                                .padding(.bottom, 5)
                        }
                    }
                    .frame(height: geometry.size.height)
                    .offset(y: -keyboardHeight)
                }
                .ignoresSafeArea()
            }
        }
        .onAppear {
            // 监听键盘显示事件
            NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillShowNotification, object: nil, queue: .main) { notification in
                if let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect {
                    withAnimation(.easeOut(duration: 0.25)) {
                        keyboardHeight = keyboardFrame.height
                    }
                }
            }
            
            // 监听键盘隐藏事件
            NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillHideNotification, object: nil, queue: .main) { _ in
                withAnimation(.easeOut(duration: 0.25)) {
                    keyboardHeight = 0
                }
            }
        }
    }
}

/**
 * 添加键盘工具栏功能的扩展
 */
extension View {
    func keyboardToolbar() -> some View {
        modifier(KeyboardToolbarModifier())
    }
}
