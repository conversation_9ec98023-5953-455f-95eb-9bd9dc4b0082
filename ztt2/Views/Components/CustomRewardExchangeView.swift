//
//  CustomRewardExchangeView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 自定义奖品兑换表单弹窗组件
 * 基于MemberPointsFormView设计，适配自定义奖品兑换场景
 *
 * ## 功能特性
 * - 奖品名称输入
 * - 积分消耗设置
 * - 表单验证和错误提示
 * - 直接兑换功能（不保存为奖品）
 * - 美观的动画效果和触觉反馈
 *
 * ## 设计模式
 * 采用MVVM模式，分离视图和数据逻辑
 *
 * ## 兼容性
 * - iOS 15.6+
 * - 支持深色模式和动态字体
 * - 完整的无障碍支持
 */
struct CustomRewardExchangeView: View {
    
    // MARK: - Properties
    @Binding var isPresented: Bool
    let memberName: String
    let currentPoints: Int
    let onExchange: (String, Int) -> Void  // 奖品名称, 积分消耗
    let onCancel: () -> Void
    
    // MARK: - State
    @State private var rewardName: String = ""
    @State private var pointsCost: String = ""
    @State private var animationTrigger = false
    @State private var showValidationErrors = false
    @State private var isExchanging = false
    @FocusState private var focusedField: FormField?
    
    // MARK: - Form Fields
    enum FormField {
        case rewardName
        case pointsCost
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        return !rewardName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !pointsCost.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               Int(pointsCost) != nil &&
               Int(pointsCost)! > 0
    }
    
    private var pointsCostValue: Int {
        return Int(pointsCost) ?? 0
    }
    
    private var canExchange: Bool {
        return isFormValid && currentPoints >= pointsCostValue
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        dismissKeyboard()
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            onCancel()
                        }
                    }
                    .transition(.opacity)
                
                // 表单对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        headerView
                        
                        // 分隔线
                        dividerView
                        
                        // 当前积分显示
                        currentPointsSection
                        
                        // 分隔线
                        dividerView
                        
                        // 表单内容
                        ScrollView {
                            VStack(spacing: 20) {
                                formContent
                                
                                // 验证错误显示
                                if showValidationErrors && !isFormValid {
                                    validationErrorsView
                                }
                                
                                // 积分不足提示
                                if isFormValid && !canExchange {
                                    insufficientPointsView
                                }
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                        }
                        
                        // 分隔线
                        dividerView
                        
                        // 底部按钮
                        bottomButtonsView
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 350))
                    .frame(maxHeight: min(geometry.size.height * 0.7, 500))
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color(hex: "#f39c12").opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
                
                // 自动聚焦到第一个输入框
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                    focusedField = .rewardName
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
                dismissKeyboard()
            }
        }
        .keyboardToolbar()
    }
    
    // MARK: - 子视图组件
    
    /**
     * 标题栏
     */
    private var headerView: some View {
        HStack {
            Text("自定义兑换")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Spacer()
            
            // 关闭按钮
            Button(action: {
                dismissKeyboard()
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onCancel()
                }
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
        .padding(.bottom, 16)
    }
    
    /**
     * 分隔线
     */
    private var dividerView: some View {
        Rectangle()
            .fill(Color(hex: "#edf5d9"))
            .frame(height: 1)
            .padding(.horizontal, 20)
    }
    
    /**
     * 当前积分显示区域
     */
    private var currentPointsSection: some View {
        HStack {
            Text("\(memberName)的当前积分")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Spacer()
            
            Text("\(currentPoints) 分")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(DesignSystem.Colors.scoreDisplay)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
    }
    
    /**
     * 表单内容
     */
    private var formContent: some View {
        VStack(spacing: 16) {
            // 奖品名称输入
            VStack(alignment: .leading, spacing: 8) {
                Text("奖品名称 *")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                TextField("请输入奖品名称", text: $rewardName)
                    .textFieldStyle(CustomRewardTextFieldStyle())
                    .focused($focusedField, equals: .rewardName)
                    .onSubmit {
                        focusedField = .pointsCost
                    }
            }
            
            // 积分消耗输入
            VStack(alignment: .leading, spacing: 8) {
                Text("积分消耗 *")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                TextField("请输入所需积分", text: $pointsCost)
                    .textFieldStyle(CustomRewardTextFieldStyle())
                    .keyboardType(.numberPad)
                    .focused($focusedField, equals: .pointsCost)
            }
        }
    }

    /**
     * 验证错误显示
     */
    private var validationErrorsView: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 8) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.errorColor)

                Text("请检查以下问题：")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.errorColor)
            }

            VStack(alignment: .leading, spacing: 4) {
                if rewardName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    Text("• 奖品名称不能为空")
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.errorColor)
                }

                if pointsCost.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    Text("• 积分消耗不能为空")
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.errorColor)
                } else if Int(pointsCost) == nil {
                    Text("• 积分消耗必须是数字")
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.errorColor)
                } else if Int(pointsCost)! <= 0 {
                    Text("• 积分消耗必须大于0")
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.errorColor)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(DesignSystem.Colors.errorColor.opacity(0.1))
        .cornerRadius(12)
        .transition(.scale.combined(with: .opacity))
    }

    /**
     * 积分不足提示
     */
    private var insufficientPointsView: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 8) {
                Image(systemName: "exclamationmark.circle.fill")
                    .font(.system(size: 16))
                    .foregroundColor(Color.orange)

                Text("积分不足")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.orange)
            }

            Text("当前积分：\(currentPoints) 分，需要：\(pointsCostValue) 分")
                .font(.system(size: 12))
                .foregroundColor(Color.orange)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
        .transition(.scale.combined(with: .opacity))
    }

    /**
     * 底部按钮
     */
    private var bottomButtonsView: some View {
        HStack(spacing: 12) {
            // 取消按钮
            Button(action: {
                dismissKeyboard()
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onCancel()
                }
            }) {
                Text("取消")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
            }

            // 兑换按钮
            Button(action: {
                exchangeReward()
            }) {
                HStack(spacing: 8) {
                    if isExchanging {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    }

                    Text(isExchanging ? "兑换中..." : "确认兑换")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 44)
                .background(
                    canExchange && !isExchanging
                    ? Color(hex: "#f39c12")
                    : Color.gray.opacity(0.3)
                )
                .cornerRadius(12)
            }
            .disabled(!canExchange || isExchanging)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }

    // MARK: - 辅助方法

    /**
     * 隐藏键盘
     */
    private func dismissKeyboard() {
        focusedField = nil
    }

    /**
     * 执行兑换
     */
    private func exchangeReward() {
        dismissKeyboard()

        // 验证表单
        if !isFormValid {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                showValidationErrors = true
            }

            // 错误触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            return
        }

        // 检查积分是否足够
        if !canExchange {
            // 积分不足触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.warning)
            return
        }

        // 执行兑换
        isExchanging = true

        // 模拟兑换延迟（实际使用中可以移除）
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isExchanging = false

            // 成功触觉反馈
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.success)

            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                onExchange(rewardName, pointsCostValue)
            }
        }
    }
}

/**
 * 自定义兑换表单文本框样式
 */
struct CustomRewardTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(hex: "#f39c12").opacity(0.3), lineWidth: 1)
            )
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()

        CustomRewardExchangeView(
            isPresented: .constant(true),
            memberName: "多多",
            currentPoints: 15,
            onExchange: { name, cost in
                print("兑换奖品: \(name), 消耗积分: \(cost)")
            },
            onCancel: {
                print("取消自定义兑换")
            }
        )
    }
}
