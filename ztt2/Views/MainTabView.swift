//
//  MainTabView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 导航状态枚举
 * 用于控制应用的页面导航状态
 */
enum NavigationState {
    case home
    case memberDetail
    case subscription
}

/**
 * 主导航容器 - 管理整个应用的页面切换
 */
struct MainTabView: View {

    // MARK: - State Management
    @StateObject private var homeViewModel = HomeViewModel()
    @StateObject private var speechService = SpeechRecognitionService()
    @State private var navigationState: NavigationState = .home
    @State private var selectedMemberId: String? = nil
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景色
                DesignSystem.Colors.background
                    .ignoresSafeArea(.all)
                
                // 主要内容区域 - 标签页内容
                if navigationState == .home {
                    VStack(spacing: 0) {
                        // 主要内容区域
                        Group {
                            switch homeViewModel.selectedTabIndex {
                            case 0:
                                HomeView(
                                    onMemberSelected: handleMemberSelection
                                )
                                .environmentObject(homeViewModel)
                            case 1:
                                GrowthDiaryView(speechService: speechService)
                            case 2:
                                ProfileView()
                            default:
                                HomeView(
                                    onMemberSelected: handleMemberSelection
                                )
                                .environmentObject(homeViewModel)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: geometry.size.height - DesignSystem.LiquidTabBar.height + geometry.safeAreaInsets.bottom)
                        .padding(.bottom, DesignSystem.LiquidTabBar.height - geometry.safeAreaInsets.bottom)
                        
                        Spacer(minLength: 0)
                    }
                    
                    // 自定义底部导航栏 - 固定在底部
                    VStack {
                        Spacer()
                        
                        CustomTabBar(
                            selectedIndex: $homeViewModel.selectedTabIndex,
                            speechService: homeViewModel.selectedTabIndex == 1 ? speechService : nil,
                            onRecognizedText: nil
                        ) { index in
                            homeViewModel.selectTab(index)
                        }
                        .frame(height: DesignSystem.LiquidTabBar.height + geometry.safeAreaInsets.bottom)
                        .padding(.bottom, -geometry.safeAreaInsets.bottom) // 负边距抵消安全区域
                    }
                    .ignoresSafeArea(.all, edges: .bottom) // 忽略所有底部安全区域
                }
                
                // 成员详情页 - 全屏覆盖
                if navigationState == .memberDetail {
                    MemberDetailView(
                        memberId: selectedMemberId,
                        onClose: handleCloseMemberDetail,
                        onNavigateToSubscription: handleNavigateToSubscription
                    )
                    .id(selectedMemberId) // 强制SwiftUI在成员ID变化时重新创建视图
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing)
                            .combined(with: .opacity)
                            .combined(with: .scale(scale: 0.95)),
                        removal: .move(edge: .trailing)
                            .combined(with: .opacity)
                            .combined(with: .scale(scale: 0.9))
                    ))
                    .zIndex(1) // 确保在最上层
                }
                
                // 订阅页面 - 全屏覆盖
                if navigationState == .subscription {
                    SubscriptionView(onDismiss: handleCloseSubscription)
                        .transition(.asymmetric(
                            insertion: .move(edge: .trailing)
                                .combined(with: .opacity)
                                .combined(with: .scale(scale: 0.95)),
                            removal: .move(edge: .trailing)
                                .combined(with: .opacity)
                                .combined(with: .scale(scale: 0.9))
                        ))
                        .zIndex(2) // 确保在成员详情页之上
                }
            }
        }
    }
    
    // MARK: - Navigation Methods
    
    /**
     * 处理家庭成员选择事件
     * @param memberId 选中的成员ID
     */
    private func handleMemberSelection(memberId: String) {
        selectedMemberId = memberId
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            navigationState = .memberDetail
        }
    }
    
    /**
     * 处理关闭成员详情页
     */
    private func handleCloseMemberDetail() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.85)) {
            navigationState = .home
        }
        
        // 立即清除选中的成员ID，避免状态不一致
        selectedMemberId = nil
    }
    
    /**
     * 处理导航到订阅页面
     */
    private func handleNavigateToSubscription() {
        // 直接跳转到订阅页面
        withAnimation(.spring(response: 0.5, dampingFraction: 0.85)) {
            navigationState = .subscription
        }
    }
    
    /**
     * 处理关闭订阅页面
     */
    private func handleCloseSubscription() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.85)) {
            navigationState = .home
        }
        
        // 延迟清除选中的成员ID，确保动画完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            selectedMemberId = nil
        }
    }
}

// MARK: - Preview
#Preview {
    MainTabView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
