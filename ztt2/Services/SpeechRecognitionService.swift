//
//  SpeechRecognitionService.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import Foundation
import Speech
import AVFoundation
import Combine

/**
 * 语音识别服务
 * 处理语音转文字功能，包括权限请求、录音和识别
 */
class SpeechRecognitionService: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var isRecording = false
    @Published var recognizedText = ""
    @Published var isAuthorized = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private var audioEngine = AVAudioEngine()
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioSession = AVAudioSession.sharedInstance()
    
    // MARK: - Initialization
    override init() {
        super.init()

        // 初始化语音识别器（中文）
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))

        if speechRecognizer == nil {
            print("无法创建中文语音识别器，尝试使用默认语言")
            speechRecognizer = SFSpeechRecognizer()
        }

        speechRecognizer?.delegate = self

        // 检查语音识别器是否可用
        if let recognizer = speechRecognizer {
            print("语音识别器创建成功，语言: \(recognizer.locale.identifier)")
            print("语音识别器可用性: \(recognizer.isAvailable)")
        } else {
            print("语音识别器创建失败")
            errorMessage = "语音识别器初始化失败"
        }

        // 检查权限状态
        checkPermissions()
    }
    
    // MARK: - Public Methods
    
    /**
     * 开始录音和语音识别
     */
    func startRecording() {
        guard isAuthorized else {
            errorMessage = "语音识别权限未授权"
            return
        }

        guard !isRecording else { return }

        // 停止之前的识别任务
        stopRecording()

        do {
            // 配置音频会话
            try configureAudioSession()

            // 创建识别请求
            recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
            guard let recognitionRequest = recognitionRequest else {
                errorMessage = "无法创建语音识别请求"
                return
            }

            // 配置识别请求
            recognitionRequest.shouldReportPartialResults = true
            recognitionRequest.requiresOnDeviceRecognition = false  // 允许使用服务器识别以提高准确性

            // 启用标点符号（仅在iOS 16+可用）
            if #available(iOS 16.0, *) {
                recognitionRequest.addsPunctuation = true
            }

            // 检查语音识别器可用性
            guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
                errorMessage = "语音识别服务不可用"
                return
            }

            // 获取音频输入节点
            let inputNode = audioEngine.inputNode

            // 创建识别任务
            recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
                DispatchQueue.main.async {
                    if let result = result {
                        self?.recognizedText = result.bestTranscription.formattedString

                        // 如果识别完成（isFinal为true），停止录音
                        if result.isFinal {
                            self?.stopRecording()
                        }
                    }

                    if let error = error {
                        print("语音识别错误: \(error)")
                        self?.errorMessage = "语音识别错误: \(error.localizedDescription)"
                        self?.stopRecording()
                    }
                }
            }

            // 配置音频格式 - 使用更好的音频格式
            let recordingFormat = inputNode.outputFormat(forBus: 0)
            inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { [weak recognitionRequest] buffer, _ in
                recognitionRequest?.append(buffer)
            }

            // 启动音频引擎
            audioEngine.prepare()
            try audioEngine.start()

            isRecording = true
            errorMessage = nil

        } catch {
            print("录音启动失败: \(error)")
            errorMessage = "录音启动失败: \(error.localizedDescription)"
        }
    }
    
    /**
     * 停止录音和语音识别
     */
    func stopRecording() {
        guard isRecording else { return }

        // 停止音频引擎
        if audioEngine.isRunning {
            audioEngine.stop()
            audioEngine.inputNode.removeTap(onBus: 0)
        }

        // 结束识别请求
        recognitionRequest?.endAudio()
        recognitionRequest = nil

        // 取消识别任务
        recognitionTask?.cancel()
        recognitionTask = nil

        // 停用音频会话
        do {
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
        } catch {
            print("停用音频会话失败: \(error)")
        }

        isRecording = false
    }
    
    /**
     * 清除识别的文本
     */
    func clearRecognizedText() {
        recognizedText = ""
    }
    
    /**
     * 请求权限
     */
    func requestPermissions() {
        // 请求语音识别权限
        SFSpeechRecognizer.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                switch status {
                case .authorized:
                    // 请求麦克风权限
                    self?.requestMicrophonePermission()
                case .denied, .restricted, .notDetermined:
                    self?.isAuthorized = false
                    self?.errorMessage = "语音识别权限被拒绝"
                @unknown default:
                    self?.isAuthorized = false
                    self?.errorMessage = "未知的权限状态"
                }
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 检查权限状态
     */
    private func checkPermissions() {
        let speechStatus = SFSpeechRecognizer.authorizationStatus()
        let microphoneStatus = AVAudioSession.sharedInstance().recordPermission

        print("语音识别权限状态: \(speechStatus.rawValue)")
        print("麦克风权限状态: \(microphoneStatus.rawValue)")

        isAuthorized = speechStatus == .authorized && microphoneStatus == .granted

        if !isAuthorized {
            print("权限未授权，开始请求权限")
            requestPermissions()
        } else {
            print("权限已授权")
        }
    }
    
    /**
     * 请求麦克风权限
     */
    private func requestMicrophonePermission() {
        audioSession.requestRecordPermission { [weak self] granted in
            DispatchQueue.main.async {
                self?.isAuthorized = granted
                if !granted {
                    self?.errorMessage = "麦克风权限被拒绝"
                }
            }
        }
    }
    
    /**
     * 配置音频会话
     */
    private func configureAudioSession() throws {
        // 使用 playAndRecord 类别以支持更多选项
        try audioSession.setCategory(.playAndRecord, mode: .spokenAudio, options: [.duckOthers, .defaultToSpeaker])
        try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
    }
}

// MARK: - SFSpeechRecognizerDelegate
extension SpeechRecognitionService: SFSpeechRecognizerDelegate {
    func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        DispatchQueue.main.async {
            if !available {
                self.errorMessage = "语音识别服务不可用"
                self.stopRecording()
            }
        }
    }
}
