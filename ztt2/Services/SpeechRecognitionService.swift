//
//  SpeechRecognitionService.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import Foundation
import Speech
import AVFoundation
import Combine

/**
 * 语音识别服务
 * 处理语音转文字功能，包括权限请求、录音和识别
 */
class SpeechRecognitionService: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var isRecording = false
    @Published var recognizedText = ""
    @Published var finalRecognizedText = ""  // 最终确定的识别结果
    @Published var isAuthorized = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private var audioEngine = AVAudioEngine()
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioSession = AVAudioSession.sharedInstance()
    
    // MARK: - Initialization
    override init() {
        super.init()

        // 初始化语音识别器（中文）
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))
        speechRecognizer?.delegate = self

        // 检查权限状态
        checkPermissions()
    }
    
    // MARK: - Public Methods
    
    /**
     * 开始录音和语音识别
     */
    func startRecording() {
        guard isAuthorized else {
            errorMessage = "语音识别权限未授权"
            return
        }
        
        guard !isRecording else { return }
        
        // 停止之前的识别任务
        stopRecording()
        
        do {
            // 配置音频会话
            try configureAudioSession()
            
            // 创建识别请求
            recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
            guard let recognitionRequest = recognitionRequest else {
                errorMessage = "无法创建语音识别请求"
                return
            }

            // 配置识别请求
            recognitionRequest.shouldReportPartialResults = true
            recognitionRequest.requiresOnDeviceRecognition = false  // 使用在线识别以提高准确性

            // 如果支持，设置任务提示为听写
            if #available(iOS 16.0, *) {
                recognitionRequest.addsPunctuation = true  // 自动添加标点符号
            }
            
            // 获取音频输入节点
            let inputNode = audioEngine.inputNode
            
            // 创建识别任务
            recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) { [weak self] result, error in
                DispatchQueue.main.async {
                    if let result = result {
                        // 只更新实时显示的文本，不立即设置为最终结果
                        self?.recognizedText = result.bestTranscription.formattedString

                        // 如果识别结果是最终的，则设置最终结果
                        if result.isFinal {
                            self?.finalRecognizedText = result.bestTranscription.formattedString
                        }
                    }

                    if let error = error {
                        self?.errorMessage = "语音识别错误: \(error.localizedDescription)"
                        self?.stopRecording()
                    }
                }
            }
            
            // 配置音频格式
            let recordingFormat = inputNode.outputFormat(forBus: 0)
            inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
                recognitionRequest.append(buffer)
            }
            
            // 启动音频引擎
            audioEngine.prepare()
            try audioEngine.start()
            
            isRecording = true
            errorMessage = nil
            
        } catch {
            errorMessage = "录音启动失败: \(error.localizedDescription)"
        }
    }
    
    /**
     * 停止录音和语音识别
     */
    func stopRecording() {
        guard isRecording else { return }

        // 停止音频引擎
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)

        // 结束识别请求，但不立即取消任务，给一点时间获取最终结果
        recognitionRequest?.endAudio()

        isRecording = false

        // 延迟一点时间后设置最终结果并清理
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            guard let self = self else { return }

            // 设置最终识别结果
            if !self.recognizedText.isEmpty && self.finalRecognizedText.isEmpty {
                self.finalRecognizedText = self.recognizedText
            }

            // 清理识别任务
            self.recognitionTask?.cancel()
            self.recognitionTask = nil
            self.recognitionRequest = nil
        }
    }
    
    /**
     * 清除识别的文本
     */
    func clearRecognizedText() {
        recognizedText = ""
        finalRecognizedText = ""
    }
    
    /**
     * 请求权限
     */
    func requestPermissions() {
        // 请求语音识别权限
        SFSpeechRecognizer.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                switch status {
                case .authorized:
                    // 请求麦克风权限
                    self?.requestMicrophonePermission()
                case .denied, .restricted, .notDetermined:
                    self?.isAuthorized = false
                    self?.errorMessage = "语音识别权限被拒绝"
                @unknown default:
                    self?.isAuthorized = false
                    self?.errorMessage = "未知的权限状态"
                }
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 检查权限状态
     */
    private func checkPermissions() {
        let speechStatus = SFSpeechRecognizer.authorizationStatus()
        let microphoneStatus = AVAudioSession.sharedInstance().recordPermission
        
        isAuthorized = speechStatus == .authorized && microphoneStatus == .granted
        
        if !isAuthorized {
            requestPermissions()
        }
    }
    
    /**
     * 请求麦克风权限
     */
    private func requestMicrophonePermission() {
        audioSession.requestRecordPermission { [weak self] granted in
            DispatchQueue.main.async {
                self?.isAuthorized = granted
                if !granted {
                    self?.errorMessage = "麦克风权限被拒绝"
                }
            }
        }
    }
    
    /**
     * 配置音频会话
     */
    private func configureAudioSession() throws {
        try audioSession.setCategory(.record, mode: .spokenAudio, options: [.duckOthers, .defaultToSpeaker])
        try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
    }
}

// MARK: - SFSpeechRecognizerDelegate
extension SpeechRecognitionService: SFSpeechRecognizerDelegate {
    func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        DispatchQueue.main.async {
            if !available {
                self.errorMessage = "语音识别服务不可用"
                self.stopRecording()
            }
        }
    }
}
