//
//  SpeechRecognitionService.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import Foundation
import Speech
import AVFoundation
import Combine

/**
 * 语音识别服务
 * 处理语音转文字功能，包括权限请求、录音和识别
 */
class SpeechRecognitionService: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var isRecording = false
    @Published var recognizedText = ""
    @Published var isAuthorized = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private var audioEngine = AVAudioEngine()
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioSession = AVAudioSession.sharedInstance()
    
    // MARK: - Initialization
    override init() {
        super.init()

        // 初始化语音识别器（中文）
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))
        speechRecognizer?.delegate = self

        // 检查权限状态
        checkPermissions()
    }
    
    // MARK: - Public Methods
    
    /**
     * 开始录音和语音识别
     */
    func startRecording() {
        guard isAuthorized else {
            errorMessage = "语音识别权限未授权"
            return
        }
        
        guard !isRecording else { return }
        
        // 停止之前的识别任务
        stopRecording()
        
        do {
            // 配置音频会话
            try configureAudioSession()
            
            // 创建识别请求
            recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
            guard let recognitionRequest = recognitionRequest else {
                errorMessage = "无法创建语音识别请求"
                return
            }
            
            recognitionRequest.shouldReportPartialResults = false  // 只返回最终结果，避免部分结果累积
            
            // 获取音频输入节点
            let inputNode = audioEngine.inputNode
            
            // 创建识别任务
            recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) { [weak self] result, error in
                DispatchQueue.main.async {
                    if let result = result {
                        // 只在识别完成时处理最终结果
                        if result.isFinal {
                            let finalText = result.bestTranscription.formattedString
                            // 添加基本的标点符号处理
                            let processedText = self?.addPunctuation(to: finalText) ?? finalText
                            self?.recognizedText = processedText
                        }
                    }

                    if let error = error {
                        self?.errorMessage = "语音识别错误: \(error.localizedDescription)"
                        self?.stopRecording()
                    }
                }
            }
            
            // 配置音频格式
            let recordingFormat = inputNode.outputFormat(forBus: 0)
            inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
                recognitionRequest.append(buffer)
            }
            
            // 启动音频引擎
            audioEngine.prepare()
            try audioEngine.start()
            
            isRecording = true
            errorMessage = nil
            
        } catch {
            errorMessage = "录音启动失败: \(error.localizedDescription)"
        }
    }
    
    /**
     * 停止录音和语音识别
     */
    func stopRecording() {
        guard isRecording else { return }
        
        // 停止音频引擎
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        
        // 结束识别请求
        recognitionRequest?.endAudio()
        recognitionRequest = nil
        
        // 取消识别任务
        recognitionTask?.cancel()
        recognitionTask = nil
        
        isRecording = false
    }
    
    /**
     * 清除识别的文本
     */
    func clearRecognizedText() {
        recognizedText = ""
    }
    
    /**
     * 请求权限
     */
    func requestPermissions() {
        // 请求语音识别权限
        SFSpeechRecognizer.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                switch status {
                case .authorized:
                    // 请求麦克风权限
                    self?.requestMicrophonePermission()
                case .denied, .restricted, .notDetermined:
                    self?.isAuthorized = false
                    self?.errorMessage = "语音识别权限被拒绝"
                @unknown default:
                    self?.isAuthorized = false
                    self?.errorMessage = "未知的权限状态"
                }
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 检查权限状态
     */
    private func checkPermissions() {
        let speechStatus = SFSpeechRecognizer.authorizationStatus()
        let microphoneStatus = AVAudioSession.sharedInstance().recordPermission
        
        isAuthorized = speechStatus == .authorized && microphoneStatus == .granted
        
        if !isAuthorized {
            requestPermissions()
        }
    }
    
    /**
     * 请求麦克风权限
     */
    private func requestMicrophonePermission() {
        audioSession.requestRecordPermission { [weak self] granted in
            DispatchQueue.main.async {
                self?.isAuthorized = granted
                if !granted {
                    self?.errorMessage = "麦克风权限被拒绝"
                }
            }
        }
    }
    
    /**
     * 配置音频会话
     */
    private func configureAudioSession() throws {
        try audioSession.setCategory(.record, mode: .measurement, options: .duckOthers)
        try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
    }

    /**
     * 为识别的文本添加基本标点符号
     */
    private func addPunctuation(to text: String) -> String {
        var processedText = text.trimmingCharacters(in: .whitespacesAndNewlines)

        // 如果文本为空，直接返回
        if processedText.isEmpty {
            return processedText
        }

        // 移除多余的空格
        processedText = processedText.replacingOccurrences(of: "\\s+", with: "", options: .regularExpression)

        // 根据常见的中文语音模式添加标点符号
        let questionWords = ["什么", "哪里", "怎么", "为什么", "谁", "哪个", "多少", "几点", "什么时候"]
        let exclamationWords = ["太好了", "真棒", "加油", "不错", "厉害", "哇", "啊"]

        // 检查是否包含疑问词
        for questionWord in questionWords {
            if processedText.contains(questionWord) {
                if !processedText.hasSuffix("？") && !processedText.hasSuffix("?") {
                    processedText += "？"
                }
                return processedText
            }
        }

        // 检查是否包含感叹词
        for exclamationWord in exclamationWords {
            if processedText.contains(exclamationWord) {
                if !processedText.hasSuffix("！") && !processedText.hasSuffix("!") {
                    processedText += "！"
                }
                return processedText
            }
        }

        // 默认添加句号
        if !processedText.hasSuffix("。") && !processedText.hasSuffix(".") &&
           !processedText.hasSuffix("？") && !processedText.hasSuffix("?") &&
           !processedText.hasSuffix("！") && !processedText.hasSuffix("!") {
            processedText += "。"
        }

        return processedText
    }
}

// MARK: - SFSpeechRecognizerDelegate
extension SpeechRecognitionService: SFSpeechRecognizerDelegate {
    func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        DispatchQueue.main.async {
            if !available {
                self.errorMessage = "语音识别服务不可用"
                self.stopRecording()
            }
        }
    }
}
